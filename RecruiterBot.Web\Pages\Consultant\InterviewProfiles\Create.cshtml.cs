using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using RecruiterBot.Core.Constants;
using RecruiterBot.Core.Models;
using RecruiterBot.Infrastructure.Services;
using System.ComponentModel.DataAnnotations;
using System.Security.Claims;
using System.Text.Json;

namespace RecruiterBot.Web.Pages.Consultant.InterviewProfiles
{
    [Authorize(Policy = AuthorizationPolicies.ConsultantOnly)]
    public class CreateModel : PageModel
    {
        private readonly IInterviewProfileService _interviewProfileService;
        private readonly ILogger<CreateModel> _logger;

        public CreateModel(IInterviewProfileService interviewProfileService, ILogger<CreateModel> logger)
        {
            _interviewProfileService = interviewProfileService;
            _logger = logger;
        }

        [BindProperty]
        public InputModel Input { get; set; } = new();

        public class InputModel
        {
            [Required(ErrorMessage = "About You section is required")]
            [StringLength(1000, ErrorMessage = "About You cannot exceed 1000 characters")]
            [Display(Name = "About You")]
            public string AboutYou { get; set; } = string.Empty;

            // JSON fields for complex data
            public string CodingLanguagesJson { get; set; } = "[]";
            public string ExperiencesJson { get; set; } = "[]";
            public string AchievementsJson { get; set; } = "[]";

            // Helper properties to deserialize JSON
            public List<string> CodingLanguages
            {
                get
                {
                    try
                    {
                        return JsonSerializer.Deserialize<List<string>>(CodingLanguagesJson) ?? new List<string>();
                    }
                    catch
                    {
                        return new List<string>();
                    }
                }
            }

            public List<WorkExperienceInput> Experiences
            {
                get
                {
                    try
                    {
                        return JsonSerializer.Deserialize<List<WorkExperienceInput>>(ExperiencesJson) ?? new List<WorkExperienceInput>();
                    }
                    catch
                    {
                        return new List<WorkExperienceInput>();
                    }
                }
            }

            public List<string> Achievements
            {
                get
                {
                    try
                    {
                        return JsonSerializer.Deserialize<List<string>>(AchievementsJson) ?? new List<string>();
                    }
                    catch
                    {
                        return new List<string>();
                    }
                }
            }
        }

        public class WorkExperienceInput
        {
            public string CompanyName { get; set; } = string.Empty;
            public string StartDate { get; set; } = string.Empty; // YYYY-MM format from month input
            public string EndDate { get; set; } = string.Empty;   // YYYY-MM format from month input
            public string ProjectDescription { get; set; } = string.Empty;
            public List<string> TechnologiesUsed { get; set; } = new List<string>();
            public bool IsCurrentProject { get; set; }
        }

        public IActionResult OnGet()
        {
            return Page();
        }

        public async Task<IActionResult> OnPostAsync()
        {
            try
            {
                // Custom validation for JSON fields
                ValidateJsonFields();

                if (!ModelState.IsValid)
                {
                    return Page();
                }

                var consultantId = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
                if (string.IsNullOrEmpty(consultantId))
                {
                    return Challenge();
                }

                // Create the interview profile
                var profile = new InterviewProfile
                {
                    AboutYou = Input.AboutYou.Trim(),
                    CodingLanguages = Input.CodingLanguages.Where(lang => !string.IsNullOrWhiteSpace(lang)).ToList(),
                    Achievements = Input.Achievements.Where(ach => !string.IsNullOrWhiteSpace(ach)).ToList(),
                    Experiences = ConvertToWorkExperiences(Input.Experiences)
                };

                var createdProfile = await _interviewProfileService.CreateProfileAsync(profile, consultantId);

                _logger.LogInformation("Consultant {ConsultantId} created interview profile {ProfileId}", 
                    consultantId, createdProfile.Id);

                return RedirectToPage("./Index", new { SuccessMessage = "Interview profile created successfully!" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating interview profile");
                ModelState.AddModelError(string.Empty, "An error occurred while creating the profile. Please try again.");
                return Page();
            }
        }

        private void ValidateJsonFields()
        {
            // Validate experiences
            var experiences = Input.Experiences;
            for (int i = 0; i < experiences.Count; i++)
            {
                var exp = experiences[i];
                
                if (string.IsNullOrWhiteSpace(exp.CompanyName))
                {
                    ModelState.AddModelError($"Experience[{i}].CompanyName", "Company name is required");
                }

                if (string.IsNullOrWhiteSpace(exp.StartDate))
                {
                    ModelState.AddModelError($"Experience[{i}].StartDate", "Start date is required");
                }

                if (!exp.IsCurrentProject && !string.IsNullOrWhiteSpace(exp.EndDate))
                {
                    if (DateTime.TryParse($"{exp.StartDate}-01", out var startDate) && 
                        DateTime.TryParse($"{exp.EndDate}-01", out var endDate))
                    {
                        if (endDate < startDate)
                        {
                            ModelState.AddModelError($"Experience[{i}].EndDate", "End date cannot be before start date");
                        }
                    }
                }

                if (!string.IsNullOrWhiteSpace(exp.ProjectDescription) && exp.ProjectDescription.Length > 2000)
                {
                    ModelState.AddModelError($"Experience[{i}].ProjectDescription", "Project description cannot exceed 2000 characters");
                }
            }

            // Validate that at least some content is provided
            if (string.IsNullOrWhiteSpace(Input.AboutYou) && 
                !Input.CodingLanguages.Any() && 
                !experiences.Any() && 
                !Input.Achievements.Any())
            {
                ModelState.AddModelError(string.Empty, "Please provide at least some profile information");
            }
        }

        private List<InterviewWorkExperience> ConvertToWorkExperiences(List<WorkExperienceInput> inputs)
        {
            var experiences = new List<InterviewWorkExperience>();

            foreach (var input in inputs)
            {
                if (string.IsNullOrWhiteSpace(input.CompanyName) || string.IsNullOrWhiteSpace(input.StartDate))
                    continue;

                var experience = new InterviewWorkExperience
                {
                    CompanyName = input.CompanyName.Trim(),
                    ProjectDescription = input.ProjectDescription?.Trim() ?? string.Empty,
                    TechnologiesUsed = input.TechnologiesUsed?.Where(t => !string.IsNullOrWhiteSpace(t)).ToList() ?? new List<string>(),
                    IsCurrentProject = input.IsCurrentProject
                };

                // Parse start date
                if (DateTime.TryParse($"{input.StartDate}-01", out var startDate))
                {
                    experience.StartDate = startDate;
                }
                else
                {
                    continue; // Skip invalid start date
                }

                // Parse end date
                if (!input.IsCurrentProject && !string.IsNullOrWhiteSpace(input.EndDate))
                {
                    if (DateTime.TryParse($"{input.EndDate}-01", out var endDate))
                    {
                        experience.EndDate = endDate;
                    }
                }

                experiences.Add(experience);
            }

            return experiences.OrderByDescending(e => e.StartDate).ToList();
        }
    }
}
