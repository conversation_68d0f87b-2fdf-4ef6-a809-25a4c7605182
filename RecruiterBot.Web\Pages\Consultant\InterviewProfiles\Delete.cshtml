@page "{id}"
@model RecruiterBot.Web.Pages.Consultant.InterviewProfiles.DeleteModel
@using RecruiterBot.Core.Constants
@{
    ViewData["Title"] = "Delete Interview Profile";
}

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <!-- Header -->
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h2 class="text-danger mb-1">
                        <i class="bi bi-trash me-2"></i>
                        Delete Interview Profile
                    </h2>
                    <p class="text-muted mb-0">Permanently remove this profile from your account</p>
                </div>
                <a asp-page="./Index" class="btn btn-secondary">
                    <i class="bi bi-arrow-left me-2"></i>
                    Back to Profiles
                </a>
            </div>

            <!-- Confirmation -->
            <div class="row justify-content-center">
                <div class="col-lg-8">
                    @if (Model.Profile != null)
                    {
                        <div class="card border-danger">
                            <div class="card-header bg-danger text-white">
                                <h5 class="mb-0">
                                    <i class="bi bi-exclamation-triangle me-2"></i>
                                    Confirm Deletion
                                </h5>
                            </div>
                            <div class="card-body">
                                <div class="alert alert-warning" role="alert">
                                    <i class="bi bi-exclamation-triangle me-2"></i>
                                    <strong>Warning:</strong> This action cannot be undone. Your interview profile will be permanently deleted.
                                </div>

                                <!-- Profile Summary -->
                                <div class="row mb-4">
                                    <div class="col-12">
                                        <h6 class="text-muted mb-3">Profile Summary</h6>
                                    </div>
                                    
                                    <!-- About You Preview -->
                                    <div class="col-12 mb-3">
                                        <div class="card bg-light">
                                            <div class="card-body py-2">
                                                <small class="text-muted">About You</small>
                                                <div>@(Model.Profile.AboutYou.Length > 200 ? Model.Profile.AboutYou.Substring(0, 200) + "..." : Model.Profile.AboutYou)</div>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Statistics -->
                                    <div class="col-md-3 mb-3">
                                        <div class="card bg-light">
                                            <div class="card-body py-2 text-center">
                                                <div class="fw-bold text-primary">@Model.Profile.ExperienceCount</div>
                                                <small class="text-muted">Experience@(Model.Profile.ExperienceCount != 1 ? "s" : "")</small>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-3 mb-3">
                                        <div class="card bg-light">
                                            <div class="card-body py-2 text-center">
                                                <div class="fw-bold text-primary">@Model.Profile.CodingLanguages.Count</div>
                                                <small class="text-muted">Technolog@(Model.Profile.CodingLanguages.Count != 1 ? "ies" : "y")</small>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-3 mb-3">
                                        <div class="card bg-light">
                                            <div class="card-body py-2 text-center">
                                                <div class="fw-bold text-primary">@Model.Profile.AchievementCount</div>
                                                <small class="text-muted">Achievement@(Model.Profile.AchievementCount != 1 ? "s" : "")</small>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-3 mb-3">
                                        <div class="card bg-light">
                                            <div class="card-body py-2 text-center">
                                                <div class="fw-bold text-primary">@Model.Profile.TotalYearsExperience</div>
                                                <small class="text-muted">Year@(Model.Profile.TotalYearsExperience != 1 ? "s" : "") Exp.</small>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Technologies Preview -->
                                    @if (Model.Profile.CodingLanguages.Any())
                                    {
                                        <div class="col-12 mb-3">
                                            <div class="card bg-light">
                                                <div class="card-body py-2">
                                                    <small class="text-muted">Technologies</small>
                                                    <div class="d-flex flex-wrap gap-1 mt-1">
                                                        @foreach (var tech in Model.Profile.CodingLanguages.Take(10))
                                                        {
                                                            <span class="badge bg-secondary">@tech</span>
                                                        }
                                                        @if (Model.Profile.CodingLanguages.Count > 10)
                                                        {
                                                            <span class="badge bg-light text-dark">+@(Model.Profile.CodingLanguages.Count - 10) more</span>
                                                        }
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    }

                                    <!-- Recent Experiences -->
                                    @if (Model.Profile.Experiences.Any())
                                    {
                                        <div class="col-12 mb-3">
                                            <div class="card bg-light">
                                                <div class="card-body py-2">
                                                    <small class="text-muted">Recent Work Experience</small>
                                                    @foreach (var exp in Model.Profile.Experiences.OrderByDescending(e => e.StartDate).Take(3))
                                                    {
                                                        <div class="mt-1">
                                                            <strong>@exp.CompanyName</strong> 
                                                            <span class="text-muted">(@exp.DateRange)</span>
                                                        </div>
                                                    }
                                                    @if (Model.Profile.Experiences.Count > 3)
                                                    {
                                                        <div class="text-muted mt-1">
                                                            <small>+@(Model.Profile.Experiences.Count - 3) more experience@(Model.Profile.Experiences.Count - 3 != 1 ? "s" : "")</small>
                                                        </div>
                                                    }
                                                </div>
                                            </div>
                                        </div>
                                    }

                                    <!-- Metadata -->
                                    <div class="col-md-6 mb-3">
                                        <div class="card bg-light">
                                            <div class="card-body py-2">
                                                <small class="text-muted">Created</small>
                                                <div>@Model.Profile.CreatedDisplay</div>
                                            </div>
                                        </div>
                                    </div>
                                    @if (Model.Profile.UpdatedAt > Model.Profile.CreatedAt)
                                    {
                                        <div class="col-md-6 mb-3">
                                            <div class="card bg-light">
                                                <div class="card-body py-2">
                                                    <small class="text-muted">Last Updated</small>
                                                    <div>@Model.Profile.LastUpdatedDisplay</div>
                                                </div>
                                            </div>
                                        </div>
                                    }
                                </div>

                                <!-- Confirmation Form -->
                                <form method="post">
                                    @Html.AntiForgeryToken()
                                    <input asp-for="ProfileId" type="hidden" />
                                    
                                    <div class="mb-4">
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" id="confirmDelete" required>
                                            <label class="form-check-label" for="confirmDelete">
                                                I understand that this action cannot be undone and want to permanently delete this interview profile.
                                            </label>
                                        </div>
                                    </div>

                                    <div class="d-flex justify-content-end gap-2">
                                        <a asp-page="./Index" class="btn btn-secondary">
                                            <i class="bi bi-x-circle me-2"></i>Cancel
                                        </a>
                                        <button type="submit" class="btn btn-danger" id="deleteBtn" disabled>
                                            <i class="bi bi-trash me-2"></i>
                                            <span class="btn-text">Delete Profile</span>
                                            <span class="spinner-border spinner-border-sm d-none" role="status"></span>
                                        </button>
                                    </div>
                                </form>
                            </div>
                        </div>
                    }
                    else
                    {
                        <div class="card border-warning">
                            <div class="card-body text-center py-5">
                                <i class="bi bi-exclamation-triangle display-1 text-warning"></i>
                                <h4 class="mt-3">Profile Not Found</h4>
                                <p class="text-muted">The requested interview profile could not be found or you don't have access to it.</p>
                                <a asp-page="./Index" class="btn btn-primary">
                                    <i class="bi bi-arrow-left me-2"></i>Back to Profiles
                                </a>
                            </div>
                        </div>
                    }
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script>
        // Enable/disable delete button based on confirmation checkbox
        document.getElementById('confirmDelete')?.addEventListener('change', function() {
            const deleteBtn = document.getElementById('deleteBtn');
            if (deleteBtn) {
                deleteBtn.disabled = !this.checked;
            }
        });

        // Form submission with loading state
        document.querySelector('form')?.addEventListener('submit', function() {
            const deleteBtn = document.getElementById('deleteBtn');
            if (deleteBtn) {
                const btnText = deleteBtn.querySelector('.btn-text');
                const spinner = deleteBtn.querySelector('.spinner-border');
                
                deleteBtn.disabled = true;
                btnText.textContent = 'Deleting...';
                spinner.classList.remove('d-none');
            }
        });
    </script>
}
