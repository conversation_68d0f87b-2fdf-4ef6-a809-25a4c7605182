using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using MongoDB.Driver;
using RecruiterBot.Core.Models;
using RecruiterBot.Core.Constants;
using RecruiterBot.Infrastructure.Data;

namespace RecruiterBot.Infrastructure.Services
{
    public class CandidateService : ICandidateService
    {
        private readonly IMongoCollection<Candidate> _candidates;
        private readonly ILogger<CandidateService> _logger;
        private readonly IRoleManagementService _roleManagementService;
        private const string CollectionName = "Candidates";

        public CandidateService(
            IOptions<MongoDbSettings> settings,
            ILogger<CandidateService> logger,
            IRoleManagementService roleManagementService)
        {
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _roleManagementService = roleManagementService ?? throw new ArgumentNullException(nameof(roleManagementService));

            if (settings?.Value == null)
                throw new ArgumentNullException(nameof(settings));

            var client = new MongoClient(settings.Value.ConnectionString);
            var database = client.GetDatabase(settings.Value.DatabaseName);
            _candidates = database.GetCollection<Candidate>(CollectionName);

            // Create indexes
            CreateIndexes();
        }

        private void CreateIndexes()
        {
            try
            {
                // Create index on tenant for multi-tenancy
                var tenantIndexKeys = Builders<Candidate>.IndexKeys.Ascending(x => x.Tenant);
                var tenantIndexModel = new CreateIndexModel<Candidate>(tenantIndexKeys);
                
                // Create compound index on tenant and email for uniqueness
                var emailIndexKeys = Builders<Candidate>.IndexKeys
                    .Ascending(x => x.Tenant)
                    .Ascending(x => x.Email);
                var emailIndexModel = new CreateIndexModel<Candidate>(emailIndexKeys);

                // Create index on created date for sorting
                var dateIndexKeys = Builders<Candidate>.IndexKeys.Descending(x => x.CreatedDateUtc);
                var dateIndexModel = new CreateIndexModel<Candidate>(dateIndexKeys);

                _candidates.Indexes.CreateMany(new[] { tenantIndexModel, emailIndexModel, dateIndexModel });
                
                _logger.LogInformation("Candidate collection indexes created successfully");
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Error creating candidate collection indexes");
            }
        }

        public async Task<Candidate> CreateCandidateAsync(Candidate candidate, string userId)
        {
            if (candidate == null)
                throw new ArgumentNullException(nameof(candidate));

            if (string.IsNullOrWhiteSpace(userId))
                throw new ArgumentException("User ID cannot be empty", nameof(userId));

            try
            {
                // Get the Corp Admin ID for this user
                var corpAdminId = await _roleManagementService.GetCorpAdminForUserAsync(userId);
                if (string.IsNullOrWhiteSpace(corpAdminId))
                    throw new InvalidOperationException($"Could not determine Corp Admin for user {userId}");

                candidate.Tenant = corpAdminId; // Set tenant to Corp Admin ID
                candidate.CreatedBy = userId;
                candidate.CreatedDateUtc = DateTime.UtcNow;
                candidate.UpdatedDateUtc = DateTime.UtcNow;

                await _candidates.InsertOneAsync(candidate);

                _logger.LogInformation("Created candidate {CandidateId} for user {UserId} with tenant {TenantId}", candidate.Id, userId, corpAdminId);
                return candidate;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating candidate for user {UserId}", userId);
                throw;
            }
        }

        public async Task<Candidate> UpdateCandidateAsync(Candidate candidate, string userId)
        {
            if (candidate == null)
                throw new ArgumentNullException(nameof(candidate));

            if (string.IsNullOrWhiteSpace(userId))
                throw new ArgumentException("User ID cannot be empty", nameof(userId));

            if (string.IsNullOrWhiteSpace(candidate.Id))
                throw new ArgumentException("Candidate ID cannot be empty", nameof(candidate.Id));

            try
            {
                // Get the Corp Admin ID for this user
                var corpAdminId = await _roleManagementService.GetCorpAdminForUserAsync(userId);
                if (string.IsNullOrWhiteSpace(corpAdminId))
                    throw new InvalidOperationException($"Could not determine Corp Admin for user {userId}");

                candidate.UpdatedDateUtc = DateTime.UtcNow;

                var filter = Builders<Candidate>.Filter.And(
                    Builders<Candidate>.Filter.Eq(x => x.Id, candidate.Id),
                    Builders<Candidate>.Filter.Eq(x => x.Tenant, corpAdminId)
                );

                var result = await _candidates.ReplaceOneAsync(filter, candidate);

                if (result.MatchedCount == 0)
                {
                    throw new InvalidOperationException("Candidate not found or access denied");
                }

                _logger.LogInformation("Updated candidate {CandidateId} for user {UserId}", candidate.Id, userId);
                return candidate;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating candidate {CandidateId} for user {UserId}", candidate.Id, userId);
                throw;
            }
        }

        public async Task<Candidate> GetCandidateAsync(string candidateId, string userId)
        {
            if (string.IsNullOrWhiteSpace(candidateId))
                throw new ArgumentException("Candidate ID cannot be empty", nameof(candidateId));

            if (string.IsNullOrWhiteSpace(userId))
                throw new ArgumentException("User ID cannot be empty", nameof(userId));

            try
            {
                // Get the Corp Admin ID for this user
                var corpAdminId = await _roleManagementService.GetCorpAdminForUserAsync(userId);
                if (string.IsNullOrWhiteSpace(corpAdminId))
                    throw new InvalidOperationException($"Could not determine Corp Admin for user {userId}");

                var filter = Builders<Candidate>.Filter.And(
                    Builders<Candidate>.Filter.Eq(x => x.Id, candidateId),
                    Builders<Candidate>.Filter.Eq(x => x.Tenant, corpAdminId)
                );

                var candidate = await _candidates.Find(filter).FirstOrDefaultAsync();
                
                if (candidate != null)
                {
                    _logger.LogDebug("Retrieved candidate {CandidateId} for user {UserId}", candidateId, userId);
                }

                return candidate;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving candidate {CandidateId} for user {UserId}", candidateId, userId);
                throw;
            }
        }

        public async Task<List<Candidate>> GetCandidatesAsync(string userId, int skip = 0, int limit = 50, bool includeInactive = false)
        {
            if (string.IsNullOrWhiteSpace(userId))
                throw new ArgumentException("User ID cannot be empty", nameof(userId));

            if (skip < 0)
                throw new ArgumentException("Skip cannot be negative", nameof(skip));

            if (limit <= 0 || limit > 100)
                throw new ArgumentException("Limit must be between 1 and 100", nameof(limit));

            try
            {
                // Check if user is Admin - Admin users don't have access to Candidates
                var userRole = await _roleManagementService.GetUserPrimaryRoleAsync(userId);
                if (userRole == UserRoles.Admin)
                {
                    _logger.LogWarning("Admin user {UserId} attempted to access Candidates", userId);
                    throw new InvalidOperationException($"Admin users cannot access Candidates");
                }

                // Get the Corp Admin ID for this user
                var corpAdminId = await _roleManagementService.GetCorpAdminForUserAsync(userId);
                if (string.IsNullOrWhiteSpace(corpAdminId))
                    throw new InvalidOperationException($"Could not determine Corp Admin for user {userId}");

                var filterBuilder = Builders<Candidate>.Filter;
                var filter = filterBuilder.Eq(x => x.Tenant, corpAdminId);

                if (!includeInactive)
                {
                    filter = filterBuilder.And(filter, filterBuilder.Eq(x => x.IsActive, true));
                }

                var sort = Builders<Candidate>.Sort.Descending(x => x.CreatedDateUtc);

                var candidates = await _candidates
                    .Find(filter)
                    .Sort(sort)
                    .Skip(skip)
                    .Limit(limit)
                    .ToListAsync();

                _logger.LogDebug("Retrieved {Count} candidates for user {UserId} (skip: {Skip}, limit: {Limit}, includeInactive: {IncludeInactive})",
                    candidates.Count, userId, skip, limit, includeInactive);

                return candidates;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving candidates for user {UserId}", userId);
                throw;
            }
        }

        public async Task<bool> DeleteCandidateAsync(string candidateId, string userId)
        {
            if (string.IsNullOrWhiteSpace(candidateId))
                throw new ArgumentException("Candidate ID cannot be empty", nameof(candidateId));

            if (string.IsNullOrWhiteSpace(userId))
                throw new ArgumentException("User ID cannot be empty", nameof(userId));

            try
            {
                // Get the Corp Admin ID for this user
                var corpAdminId = await _roleManagementService.GetCorpAdminForUserAsync(userId);
                if (string.IsNullOrWhiteSpace(corpAdminId))
                    throw new InvalidOperationException($"Could not determine Corp Admin for user {userId}");

                var filter = Builders<Candidate>.Filter.And(
                    Builders<Candidate>.Filter.Eq(x => x.Id, candidateId),
                    Builders<Candidate>.Filter.Eq(x => x.Tenant, corpAdminId)
                );

                var result = await _candidates.DeleteOneAsync(filter);

                if (result.DeletedCount > 0)
                {
                    _logger.LogInformation("Deleted candidate {CandidateId} for user {UserId}", candidateId, userId);
                    return true;
                }

                _logger.LogWarning("Candidate {CandidateId} not found for user {UserId}", candidateId, userId);
                return false;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting candidate {CandidateId} for user {UserId}", candidateId, userId);
                throw;
            }
        }

        public async Task<bool> DeactivateCandidateAsync(string candidateId, string userId, string reason = null)
        {
            if (string.IsNullOrWhiteSpace(candidateId))
                throw new ArgumentException("Candidate ID cannot be empty", nameof(candidateId));

            if (string.IsNullOrWhiteSpace(userId))
                throw new ArgumentException("User ID cannot be empty", nameof(userId));

            try
            {
                // Get the Corp Admin ID for this user
                var corpAdminId = await _roleManagementService.GetCorpAdminForUserAsync(userId);
                if (string.IsNullOrWhiteSpace(corpAdminId))
                    throw new InvalidOperationException($"Could not determine Corp Admin for user {userId}");

                var filter = Builders<Candidate>.Filter.And(
                    Builders<Candidate>.Filter.Eq(x => x.Id, candidateId),
                    Builders<Candidate>.Filter.Eq(x => x.Tenant, corpAdminId)
                );

                var update = Builders<Candidate>.Update
                    .Set(x => x.IsActive, false)
                    .Set(x => x.DeactivatedDateUtc, DateTime.UtcNow)
                    .Set(x => x.DeactivatedBy, userId)
                    .Set(x => x.DeactivationReason, reason)
                    .Set(x => x.UpdatedDateUtc, DateTime.UtcNow);

                var result = await _candidates.UpdateOneAsync(filter, update);

                if (result.MatchedCount > 0)
                {
                    _logger.LogInformation("Deactivated candidate {CandidateId} for user {UserId}", candidateId, userId);
                    return true;
                }

                _logger.LogWarning("Candidate {CandidateId} not found for user {UserId}", candidateId, userId);
                return false;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deactivating candidate {CandidateId} for user {UserId}", candidateId, userId);
                throw;
            }
        }

        public async Task<bool> ActivateCandidateAsync(string candidateId, string userId)
        {
            if (string.IsNullOrWhiteSpace(candidateId))
                throw new ArgumentException("Candidate ID cannot be empty", nameof(candidateId));

            if (string.IsNullOrWhiteSpace(userId))
                throw new ArgumentException("User ID cannot be empty", nameof(userId));

            try
            {
                // Get the Corp Admin ID for this user
                var corpAdminId = await _roleManagementService.GetCorpAdminForUserAsync(userId);
                if (string.IsNullOrWhiteSpace(corpAdminId))
                    throw new InvalidOperationException($"Could not determine Corp Admin for user {userId}");

                var filter = Builders<Candidate>.Filter.And(
                    Builders<Candidate>.Filter.Eq(x => x.Id, candidateId),
                    Builders<Candidate>.Filter.Eq(x => x.Tenant, corpAdminId)
                );

                var update = Builders<Candidate>.Update
                    .Set(x => x.IsActive, true)
                    .Unset(x => x.DeactivatedDateUtc)
                    .Unset(x => x.DeactivatedBy)
                    .Unset(x => x.DeactivationReason)
                    .Set(x => x.UpdatedDateUtc, DateTime.UtcNow);

                var result = await _candidates.UpdateOneAsync(filter, update);

                if (result.MatchedCount > 0)
                {
                    _logger.LogInformation("Activated candidate {CandidateId} for user {UserId}", candidateId, userId);
                    return true;
                }

                _logger.LogWarning("Candidate {CandidateId} not found for user {UserId}", candidateId, userId);
                return false;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error activating candidate {CandidateId} for user {UserId}", candidateId, userId);
                throw;
            }
        }

        public async Task<long> GetCandidateCountAsync(string userId, bool includeInactive = false)
        {
            if (string.IsNullOrWhiteSpace(userId))
                throw new ArgumentException("User ID cannot be empty", nameof(userId));

            try
            {
                // Get the Corp Admin ID for this user
                var corpAdminId = await _roleManagementService.GetCorpAdminForUserAsync(userId);
                if (string.IsNullOrWhiteSpace(corpAdminId))
                    throw new InvalidOperationException($"Could not determine Corp Admin for user {userId}");

                var filterBuilder = Builders<Candidate>.Filter;
                var filter = filterBuilder.Eq(x => x.Tenant, corpAdminId);

                if (!includeInactive)
                {
                    filter = filterBuilder.And(filter, filterBuilder.Eq(x => x.IsActive, true));
                }

                var count = await _candidates.CountDocumentsAsync(filter);

                _logger.LogDebug("Candidate count for user {UserId}: {Count} (includeInactive: {IncludeInactive})", userId, count, includeInactive);
                return count;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting candidate count for user {UserId}", userId);
                throw;
            }
        }

        public async Task<List<Candidate>> GetTeamCandidatesAsync(string corpAdminId, int skip = 0, int limit = 50, bool includeInactive = false)
        {
            if (string.IsNullOrWhiteSpace(corpAdminId))
                throw new ArgumentException("Corp Admin ID cannot be empty", nameof(corpAdminId));

            if (skip < 0)
                throw new ArgumentException("Skip cannot be negative", nameof(skip));

            if (limit <= 0 || limit > 100)
                throw new ArgumentException("Limit must be between 1 and 100", nameof(limit));

            try
            {
                var filterBuilder = Builders<Candidate>.Filter;
                // Since tenant field now contains Corp Admin ID, simply query by Corp Admin ID
                var filter = filterBuilder.Eq(x => x.Tenant, corpAdminId);

                if (!includeInactive)
                {
                    filter = filterBuilder.And(filter, filterBuilder.Eq(x => x.IsActive, true));
                }

                var sort = Builders<Candidate>.Sort.Descending(x => x.CreatedDateUtc);

                var candidates = await _candidates
                    .Find(filter)
                    .Sort(sort)
                    .Skip(skip)
                    .Limit(limit)
                    .ToListAsync();

                _logger.LogDebug("Retrieved {Count} team candidates for Corp Admin {CorpAdminId} (skip: {Skip}, limit: {Limit}, includeInactive: {IncludeInactive})",
                    candidates.Count, corpAdminId, skip, limit, includeInactive);

                return candidates;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving team candidates for Corp Admin {CorpAdminId}", corpAdminId);
                throw;
            }
        }

        public async Task<List<Candidate>> GetCandidatesForUsersAsync(IEnumerable<string> userIds, int skip = 0, int limit = 50, bool includeInactive = false)
        {
            if (userIds == null || !userIds.Any())
                return new List<Candidate>();

            if (skip < 0)
                throw new ArgumentException("Skip cannot be negative", nameof(skip));

            if (limit <= 0 || limit > 100)
                throw new ArgumentException("Limit must be between 1 and 100", nameof(limit));

            try
            {
                var userIdList = userIds.ToList();
                var filterBuilder = Builders<Candidate>.Filter;
                var filter = filterBuilder.In(x => x.Tenant, userIdList);

                if (!includeInactive)
                {
                    filter = filterBuilder.And(filter, filterBuilder.Eq(x => x.IsActive, true));
                }

                var sort = Builders<Candidate>.Sort.Descending(x => x.CreatedDateUtc);

                var candidates = await _candidates
                    .Find(filter)
                    .Sort(sort)
                    .Skip(skip)
                    .Limit(limit)
                    .ToListAsync();

                _logger.LogDebug("Retrieved {Count} candidates for {UserCount} users (skip: {Skip}, limit: {Limit}, includeInactive: {IncludeInactive})",
                    candidates.Count, userIdList.Count, skip, limit, includeInactive);

                return candidates;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving candidates for users");
                throw;
            }
        }

        public async Task<List<Candidate>> GetUserOwnCandidatesAsync(string userId, int skip = 0, int limit = 50, bool includeInactive = false)
        {
            if (string.IsNullOrWhiteSpace(userId))
                throw new ArgumentException("User ID cannot be empty", nameof(userId));

            if (skip < 0)
                throw new ArgumentException("Skip cannot be negative", nameof(skip));

            if (limit <= 0 || limit > 100)
                throw new ArgumentException("Limit must be between 1 and 100", nameof(limit));

            try
            {
                var filterBuilder = Builders<Candidate>.Filter;
                // Filter by CreatedBy field to get only candidates created by this specific user
                var filter = filterBuilder.Eq(x => x.CreatedBy, userId);

                if (!includeInactive)
                {
                    filter = filterBuilder.And(filter, filterBuilder.Eq(x => x.IsActive, true));
                }

                var sort = Builders<Candidate>.Sort.Descending(x => x.CreatedDateUtc);

                var candidates = await _candidates
                    .Find(filter)
                    .Sort(sort)
                    .Skip(skip)
                    .Limit(limit)
                    .ToListAsync();

                _logger.LogDebug("Retrieved {Count} own candidates for user {UserId} (skip: {Skip}, limit: {Limit}, includeInactive: {IncludeInactive})",
                    candidates.Count, userId, skip, limit, includeInactive);

                return candidates;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving own candidates for user {UserId}", userId);
                throw;
            }
        }

        public async Task<long> GetUserOwnCandidateCountAsync(string userId, bool includeInactive = false)
        {
            if (string.IsNullOrWhiteSpace(userId))
                throw new ArgumentException("User ID cannot be empty", nameof(userId));

            try
            {
                var filterBuilder = Builders<Candidate>.Filter;
                // Filter by CreatedBy field to get only candidates created by this specific user
                var filter = filterBuilder.Eq(x => x.CreatedBy, userId);

                if (!includeInactive)
                {
                    filter = filterBuilder.And(filter, filterBuilder.Eq(x => x.IsActive, true));
                }

                var count = await _candidates.CountDocumentsAsync(filter);

                _logger.LogDebug("Own candidate count for user {UserId}: {Count} (includeInactive: {IncludeInactive})", userId, count, includeInactive);
                return count;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting own candidate count for user {UserId}", userId);
                throw;
            }
        }

        public async Task<long> GetTeamCandidateCountAsync(string corpAdminId, bool includeInactive = false)
        {
            if (string.IsNullOrWhiteSpace(corpAdminId))
                throw new ArgumentException("Corp Admin ID cannot be empty", nameof(corpAdminId));

            try
            {
                var filterBuilder = Builders<Candidate>.Filter;
                // Since tenant field now contains Corp Admin ID, simply query by Corp Admin ID
                var filter = filterBuilder.Eq(x => x.Tenant, corpAdminId);

                if (!includeInactive)
                {
                    filter = filterBuilder.And(filter, filterBuilder.Eq(x => x.IsActive, true));
                }

                var count = await _candidates.CountDocumentsAsync(filter);

                _logger.LogDebug("Team candidate count for Corp Admin {CorpAdminId}: {Count} (includeInactive: {IncludeInactive})",
                    corpAdminId, count, includeInactive);
                return count;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting team candidate count for Corp Admin {CorpAdminId}", corpAdminId);
                throw;
            }
        }
    }
}
