using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using RecruiterBot.Core.Models;
using RecruiterBot.Web.Services;
using System.ComponentModel.DataAnnotations;
using System.Threading.Tasks;

namespace RecruiterBot.Web.Pages.Account
{
    [AllowAnonymous]
    public class ResendEmailConfirmationModel : PageModel
    {
        private readonly UserManager<User> _userManager;
        private readonly IEmailService _emailService;
        private readonly ILogger<ResendEmailConfirmationModel> _logger;

        public ResendEmailConfirmationModel(
            UserManager<User> userManager,
            IEmailService emailService,
            ILogger<ResendEmailConfirmationModel> logger)
        {
            _userManager = userManager;
            _emailService = emailService;
            _logger = logger;
        }

        [BindProperty]
        public InputModel Input { get; set; } = new InputModel();

        public class InputModel
        {
            [Required]
            [EmailAddress]
            [Display(Name = "Email")]
            public string Email { get; set; } = string.Empty;
        }

        public void OnGet()
        {
        }

        public async Task<IActionResult> OnPostAsync()
        {
            if (!ModelState.IsValid)
            {
                return Page();
            }

            try
            {
                var user = await _userManager.FindByEmailAsync(Input.Email);
                if (user == null)
                {
                    // Don't reveal that the user does not exist
                    TempData["SuccessMessage"] = "If an account with that email exists, a confirmation email has been sent.";
                    return RedirectToPage("./Login");
                }

                if (user.EmailConfirmed)
                {
                    TempData["InfoMessage"] = "Your email is already confirmed. You can sign in now.";
                    return RedirectToPage("./Login");
                }

                // Generate email confirmation token
                var token = await _userManager.GenerateEmailConfirmationTokenAsync(user);
                var confirmationUrl = Url.Page(
                    "/Account/ConfirmEmail",
                    pageHandler: null,
                    values: new { userId = user.Id, token = token },
                    protocol: Request.Scheme);

                // Send email verification
                var emailSent = await _emailService.SendEmailVerificationAsync(
                    user.Email,
                    user.FirstName,
                    confirmationUrl);

                if (emailSent)
                {
                    TempData["SuccessMessage"] = "Confirmation email sent! Please check your email to verify your account.";
                    _logger.LogInformation("Email confirmation resent to user {UserId}", user.Id);
                }
                else
                {
                    TempData["ErrorMessage"] = "Failed to send confirmation email. Please try again or contact support.";
                    _logger.LogError("Failed to resend email confirmation to user {UserId}", user.Id);
                }

                return RedirectToPage("./Login");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error resending email confirmation for email {Email}", Input.Email);
                ModelState.AddModelError(string.Empty, "An error occurred while sending the confirmation email. Please try again.");
                return Page();
            }
        }
    }
}
