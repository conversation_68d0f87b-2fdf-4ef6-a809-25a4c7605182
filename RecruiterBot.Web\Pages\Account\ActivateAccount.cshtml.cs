using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using RecruiterBot.Core.Models;
using RecruiterBot.Web.Services;

namespace RecruiterBot.Web.Pages.Account
{
    public class ActivateAccountModel : PageModel
    {
        private readonly UserManager<User> _userManager;
        private readonly IEmailService _emailService;
        private readonly ILogger<ActivateAccountModel> _logger;

        public ActivateAccountModel(
            UserManager<User> userManager,
            IEmailService emailService,
            ILogger<ActivateAccountModel> logger)
        {
            _userManager = userManager;
            _emailService = emailService;
            _logger = logger;
        }

        public string UserId { get; set; }
        public string Token { get; set; }
        public bool IsValidToken { get; set; }
        public User User { get; set; }

        public async Task<IActionResult> OnGetAsync(string userId, string token)
        {
            if (string.IsNullOrEmpty(userId) || string.IsNullOrEmpty(token))
            {
                TempData["ErrorMessage"] = "Invalid activation link.";
                return RedirectToPage("./Login");
            }

            var user = await _userManager.FindByIdAsync(userId);
            if (user == null)
            {
                TempData["ErrorMessage"] = "Invalid activation link.";
                return RedirectToPage("./Login");
            }

            // Check if token matches and hasn't expired
            if (user.ActivationToken != token || 
                user.ActivationTokenExpiry == null || 
                user.ActivationTokenExpiry < DateTime.UtcNow)
            {
                TempData["ErrorMessage"] = "This activation link has expired or is invalid. Please contact your administrator for a new activation link.";
                return RedirectToPage("./Login");
            }

            // Check if already activated
            if (user.IsActivated)
            {
                TempData["InfoMessage"] = "Your account is already activated. You can log in normally.";
                return RedirectToPage("./Login");
            }

            UserId = userId;
            Token = token;
            User = user;
            IsValidToken = true;

            return Page();
        }

        public async Task<IActionResult> OnPostAsync(string userId, string token)
        {
            if (string.IsNullOrEmpty(userId) || string.IsNullOrEmpty(token))
            {
                TempData["ErrorMessage"] = "Invalid activation request.";
                return RedirectToPage("./Login");
            }

            var user = await _userManager.FindByIdAsync(userId);
            if (user == null)
            {
                TempData["ErrorMessage"] = "Invalid activation request.";
                return RedirectToPage("./Login");
            }

            // Verify token again
            if (user.ActivationToken != token || 
                user.ActivationTokenExpiry == null || 
                user.ActivationTokenExpiry < DateTime.UtcNow)
            {
                TempData["ErrorMessage"] = "This activation link has expired or is invalid.";
                return RedirectToPage("./Login");
            }

            try
            {
                // Activate the account
                user.IsActivated = true;
                user.EmailConfirmed = true;
                user.ActivationToken = null;
                user.ActivationTokenExpiry = null;

                var result = await _userManager.UpdateAsync(user);
                if (result.Succeeded)
                {
                    _logger.LogInformation("Account activated successfully for user {UserId}", user.Id);

                    // Send welcome email
                    try
                    {
                        var loginUrl = Url.Page("/Account/Login", pageHandler: null, values: null, protocol: Request.Scheme);
                        await _emailService.SendWelcomeEmailAsync(user.Email, user.FirstName, loginUrl);
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "Failed to send welcome email to {Email}", user.Email);
                    }

                    TempData["SuccessMessage"] = "Your account has been activated successfully! You can now log in with your temporary credentials. You will be required to change your password on first login.";
                    return RedirectToPage("./Login");
                }
                else
                {
                    _logger.LogError("Failed to activate account for user {UserId}. Errors: {Errors}", 
                        user.Id, string.Join(", ", result.Errors.Select(e => e.Description)));
                    TempData["ErrorMessage"] = "An error occurred while activating your account. Please try again or contact support.";
                    return Page();
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error activating account for user {UserId}", user.Id);
                TempData["ErrorMessage"] = "An error occurred while activating your account. Please try again or contact support.";
                return Page();
            }
        }
    }
}
