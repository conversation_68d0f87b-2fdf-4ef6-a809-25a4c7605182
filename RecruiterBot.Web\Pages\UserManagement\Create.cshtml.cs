using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using RecruiterBot.Core.Constants;
using RecruiterBot.Core.Models;
using RecruiterBot.Infrastructure.Services;
using RecruiterBot.Web.Services;
using System.ComponentModel.DataAnnotations;
using System.Security.Claims;
using System.Threading.Tasks;

namespace RecruiterBot.Web.Pages.UserManagement
{
    [Authorize(Policy = AuthorizationPolicies.CanCreateUsers)]
    public class CreateModel : PageModel
    {
        private readonly UserManager<User> _userManager;
        private readonly IRoleManagementService _roleManagementService;
        private readonly IEmailService _emailService;
        private readonly IPasswordGeneratorService _passwordGeneratorService;
        private readonly ILogger<CreateModel> _logger;

        public CreateModel(
            UserManager<User> userManager,
            IRoleManagementService roleManagementService,
            IEmailService emailService,
            IPasswordGeneratorService passwordGeneratorService,
            ILogger<CreateModel> logger)
        {
            _userManager = userManager;
            _roleManagementService = roleManagementService;
            _emailService = emailService;
            _passwordGeneratorService = passwordGeneratorService;
            _logger = logger;
        }

        [BindProperty]
        public InputModel Input { get; set; } = new();

        public IEnumerable<string> AvailableRoles { get; set; } = new List<string>();
        public string CurrentUserRole { get; set; }

        public class InputModel
        {
            [Required]
            [Display(Name = "First Name")]
            [StringLength(50)]
            public string FirstName { get; set; }

            [Required]
            [Display(Name = "Last Name")]
            [StringLength(50)]
            public string LastName { get; set; }

            [Required]
            [EmailAddress]
            [Display(Name = "Email")]
            public string Email { get; set; }

            [Required]
            [Display(Name = "Role")]
            public string Role { get; set; }

            [Display(Name = "Send Activation Email")]
            public bool SendActivationEmail { get; set; } = true;
        }

        public async Task<IActionResult> OnGetAsync()
        {
            var currentUserId = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
            if (string.IsNullOrEmpty(currentUserId))
            {
                return Challenge();
            }

            CurrentUserRole = await _roleManagementService.GetUserPrimaryRoleAsync(currentUserId);
            AvailableRoles = await _roleManagementService.GetCreatableRolesForUserAsync(currentUserId);

            if (!AvailableRoles.Any())
            {
                TempData["ErrorMessage"] = "You don't have permission to create users.";
                return RedirectToPage("./Index");
            }

            // Pre-select the appropriate role based on current user
            if (CurrentUserRole == UserRoles.Admin && AvailableRoles.Contains(UserRoles.CorpAdmin))
            {
                Input.Role = UserRoles.CorpAdmin;
            }
            else if (CurrentUserRole == UserRoles.CorpAdmin && AvailableRoles.Contains(UserRoles.User))
            {
                Input.Role = UserRoles.User;
            }

            return Page();
        }

        public async Task<IActionResult> OnPostAsync()
        {
            var currentUserId = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
            if (string.IsNullOrEmpty(currentUserId))
            {
                return Challenge();
            }

            CurrentUserRole = await _roleManagementService.GetUserPrimaryRoleAsync(currentUserId);
            AvailableRoles = await _roleManagementService.GetCreatableRolesForUserAsync(currentUserId);

            // Validate that the current user can create the requested role
            if (!await _roleManagementService.CanUserCreateRole(currentUserId, Input.Role))
            {
                ModelState.AddModelError("Input.Role", "You don't have permission to create users with this role.");
            }

            // Check domain restriction for Corp Admin creation
            if (Input.Role == UserRoles.CorpAdmin)
            {
                var canCreateCorpAdmin = await _roleManagementService.CanCreateCorpAdminForDomainAsync(Input.Email);
                if (!canCreateCorpAdmin)
                {
                    var domain = Input.Email.Split('@')[1];
                    var existingCorpAdmin = await _roleManagementService.GetCorpAdminByDomainAsync(domain);
                    ModelState.AddModelError(string.Empty,
                        $"A Corp Admin already exists for the domain '{domain}'. " +
                        $"Only one Corp Admin per domain is allowed. " +
                        $"Existing Corp Admin: {existingCorpAdmin?.Email}");
                }
            }

            if (!ModelState.IsValid)
            {
                return Page();
            }

            // Generate temporary password and activation token
            var temporaryPassword = _passwordGeneratorService.GenerateTemporaryPassword();
            var activationToken = _passwordGeneratorService.GenerateActivationToken();

            var user = new User
            {
                UserName = Input.Email,
                Email = Input.Email,
                FirstName = Input.FirstName,
                LastName = Input.LastName,
                CreatedBy = currentUserId,
                EmailConfirmed = false, // Require activation
                MustChangePassword = true,
                IsTemporaryPassword = true,
                ActivationToken = activationToken,
                ActivationTokenExpiry = DateTime.UtcNow.AddHours(24),
                IsActivated = false
            };

            var result = await _userManager.CreateAsync(user, temporaryPassword);

            if (result.Succeeded)
            {
                // Assign the role
                var roleAssigned = await _roleManagementService.AssignRoleToUserAsync(user.Id, Input.Role);
                
                if (roleAssigned)
                {
                    _logger.LogInformation("User {Email} created successfully with role {Role} by {CreatorId}",
                        user.Email, Input.Role, currentUserId);

                    // Send activation email if requested
                    if (Input.SendActivationEmail)
                    {
                        try
                        {
                            var setupUrl = Url.Page("/Account/SetupAccount",
                                pageHandler: null,
                                values: new { userId = user.Id, token = activationToken },
                                protocol: Request.Scheme);

                            var emailSent = await _emailService.SendAccountActivationAsync(
                                user.Email,
                                user.FirstName,
                                temporaryPassword,
                                setupUrl,
                                CurrentUserRole);

                            if (emailSent)
                            {
                                TempData["SuccessMessage"] = $"User {user.Email} has been created successfully. An activation email has been sent.";
                            }
                            else
                            {
                                TempData["WarningMessage"] = "User created successfully, but failed to send activation email. Please contact the user manually.";
                            }
                        }
                        catch (Exception ex)
                        {
                            _logger.LogError(ex, "Failed to send activation email to {Email}", user.Email);
                            TempData["WarningMessage"] = "User created successfully, but failed to send activation email. Please contact the user manually.";
                        }
                    }
                    else
                    {
                        TempData["SuccessMessage"] = $"User {user.Email} has been created successfully. Temporary password: {temporaryPassword}";
                        _logger.LogInformation("User {Email} created with temporary password: {TempPassword}", user.Email, temporaryPassword);
                    }

                    return RedirectToPage("./Index");
                }
                else
                {
                    // If role assignment failed, delete the user
                    await _userManager.DeleteAsync(user);
                    ModelState.AddModelError(string.Empty, "Failed to assign role to user.");
                }
            }

            foreach (var error in result.Errors)
            {
                ModelState.AddModelError(string.Empty, error.Description);
            }

            return Page();
        }
    }
}
