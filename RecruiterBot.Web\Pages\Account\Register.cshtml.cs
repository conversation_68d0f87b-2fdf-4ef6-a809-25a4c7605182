using System.ComponentModel.DataAnnotations;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authentication;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using RecruiterBot.Core.Models;
using RecruiterBot.Core.Constants;
using RecruiterBot.Web.Services;
using RecruiterBot.Infrastructure.Services;

namespace RecruiterBot.Web.Pages.Account
{
    [AllowAnonymous]
    public class RegisterModel : PageModel
    {
        private readonly SignInManager<User> _signInManager;
        private readonly UserManager<User> _userManager;
        private readonly ILogger<RegisterModel> _logger;
        private readonly IEmailService _emailService;
        private readonly IRoleManagementService _roleManagementService;
        private readonly IPasswordGeneratorService _passwordGeneratorService;

        public RegisterModel(
            UserManager<User> userManager,
            SignInManager<User> signInManager,
            ILogger<RegisterModel> logger,
            IEmailService emailService,
            IRoleManagementService roleManagementService,
            IPasswordGeneratorService passwordGeneratorService)
        {
            _userManager = userManager;
            _signInManager = signInManager;
            _logger = logger;
            _emailService = emailService;
            _roleManagementService = roleManagementService;
            _passwordGeneratorService = passwordGeneratorService;
            Input = new InputModel();
        }

        [BindProperty]
        public InputModel Input { get; set; } = new();

        public string ReturnUrl { get; set; }

        public IList<AuthenticationScheme> ExternalLogins { get; set; }

        public class InputModel
        {
            [Required]
            [Display(Name = "First Name")]
            public string FirstName { get; set; }

            [Required]
            [Display(Name = "Last Name")]
            public string LastName { get; set; }

            [Required]
            [EmailAddress]
            [Display(Name = "Email")]
            public string Email { get; set; }
        }


        public async Task OnGetAsync(string? returnUrl = null)
        {
            ReturnUrl = returnUrl ?? "/";
            ExternalLogins = (await _signInManager.GetExternalAuthenticationSchemesAsync()).ToList();
        }

        public async Task<IActionResult> OnPostAsync(string? returnUrl = null)
        {
            returnUrl ??= Url.Content("~/");
            ExternalLogins = (await _signInManager.GetExternalAuthenticationSchemesAsync()).ToList();
            
            if (ModelState.IsValid)
            {
                // Check if a Corp Admin already exists for this domain
                var canCreateCorpAdmin = await _roleManagementService.CanCreateCorpAdminForDomainAsync(Input.Email);
                if (!canCreateCorpAdmin)
                {
                    var domain = Input.Email.Split('@')[1];
                    var existingCorpAdmin = await _roleManagementService.GetCorpAdminByDomainAsync(domain);
                    ModelState.AddModelError(string.Empty,
                        $"An Admin already exists for the domain '{domain}'. " +
                        $"Only one Admin per company is allowed. " +
                        $"Please contact the existing Admin of your company or use a different email domain.");
                    return Page();
                }

                // Generate temporary password and activation token for Corp Admin registration
                var temporaryPassword = _passwordGeneratorService.GenerateTemporaryPassword();
                var activationToken = _passwordGeneratorService.GenerateActivationToken();

                var user = new User
                {
                    UserName = Input.Email,
                    Email = Input.Email,
                    FirstName = Input.FirstName,
                    LastName = Input.LastName,
                    EmailConfirmed = false, // Require setup completion
                    MustChangePassword = true,
                    IsTemporaryPassword = true,
                    ActivationToken = activationToken,
                    ActivationTokenExpiry = DateTime.UtcNow.AddHours(24),
                    IsActivated = false
                };

                var result = await _userManager.CreateAsync(user, temporaryPassword);
                
                if (result.Succeeded)
                {
                    _logger.LogInformation("Corp Admin user registered: {Email}", user.Email);

                    // Assign Corp Admin role
                    var roleAssigned = await _roleManagementService.AssignRoleToUserAsync(user.Id, UserRoles.CorpAdmin);
                    if (!roleAssigned)
                    {
                        _logger.LogError("Failed to assign Corp Admin role to user {UserId}", user.Id);
                        // Continue anyway, role can be assigned later by admin
                    }

                    // Generate setup URL
                    var setupUrl = Url.Page("/Account/SetupAccount",
                        pageHandler: null,
                        values: new { userId = user.Id, token = activationToken, registration = true },
                        protocol: Request.Scheme);

                    // Send registration setup email
                    var emailSent = await _emailService.SendAccountActivationAsync(
                        user.Email,
                        user.FirstName,
                        temporaryPassword,
                        setupUrl,
                        "REGISTRATION"); // Special role indicator for registration emails

                    if (emailSent)
                    {
                        _logger.LogInformation("Registration setup email sent to {Email}", user.Email);
                        return RedirectToPage("./RegisterConfirmation", new { email = user.Email });
                    }
                    else
                    {
                        _logger.LogError("Failed to send registration setup email to {Email}", user.Email);
                        TempData["ErrorMessage"] = "Account created but failed to send setup email. Please contact support.";
                        return RedirectToPage("./RegisterConfirmation", new { email = user.Email });
                    }
                }
                
                foreach (var error in result.Errors)
                {
                    ModelState.AddModelError(string.Empty, error.Description);
                }
            }

            // If we got this far, something failed, redisplay form
            return Page();
        }
    }
}
