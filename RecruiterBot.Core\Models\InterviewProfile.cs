using MongoDB.Bson;
using MongoDB.Bson.Serialization.Attributes;
using System.ComponentModel.DataAnnotations;

namespace RecruiterBot.Core.Models
{
    [BsonIgnoreExtraElements]
    public class InterviewProfile
    {
        [BsonId]
        [BsonRepresentation(BsonType.ObjectId)]
        public string Id { get; set; }

        [BsonElement("about_you")]
        [Required(ErrorMessage = "About You section is required")]
        [StringLength(1000, ErrorMessage = "About You cannot exceed 1000 characters")]
        [Display(Name = "About You")]
        public string AboutYou { get; set; } = string.Empty;

        [BsonElement("coding_languages")]
        [Display(Name = "Coding Languages & Technologies")]
        public List<string> CodingLanguages { get; set; } = new List<string>();

        [BsonElement("experiences")]
        [Display(Name = "Work Experience")]
        public List<InterviewWorkExperience> Experiences { get; set; } = new List<InterviewWorkExperience>();

        [BsonElement("achievements")]
        [Display(Name = "Professional Achievements")]
        public List<string> Achievements { get; set; } = new List<string>();

        [BsonElement("created_by")]
        [Required]
        public string CreatedBy { get; set; } = string.Empty;

        [BsonElement("created_at")]
        [BsonDateTimeOptions(Kind = DateTimeKind.Utc)]
        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

        [BsonElement("updated_at")]
        [BsonDateTimeOptions(Kind = DateTimeKind.Utc)]
        public DateTime UpdatedAt { get; set; } = DateTime.UtcNow;

        [BsonElement("tenant")]
        public string Tenant { get; set; } = string.Empty;

        // Helper properties for display
        [BsonIgnore]
        public string CodingLanguagesDisplay => string.Join(", ", CodingLanguages);

        [BsonIgnore]
        public int ExperienceCount => Experiences?.Count ?? 0;

        [BsonIgnore]
        public int AchievementCount => Achievements?.Count ?? 0;

        [BsonIgnore]
        public int TotalYearsExperience
        {
            get
            {
                if (Experiences == null || !Experiences.Any())
                    return 0;

                var totalMonths = 0;
                foreach (var exp in Experiences)
                {
                    var endDate = exp.IsCurrentProject ? DateTime.UtcNow : (exp.EndDate ?? DateTime.UtcNow);
                    var months = ((endDate.Year - exp.StartDate.Year) * 12) + endDate.Month - exp.StartDate.Month;
                    totalMonths += Math.Max(0, months);
                }

                return totalMonths / 12;
            }
        }

        [BsonIgnore]
        public string LastUpdatedDisplay => UpdatedAt.ToString("MMM dd, yyyy 'at' h:mm tt");

        [BsonIgnore]
        public string CreatedDisplay => CreatedAt.ToString("MMM dd, yyyy 'at' h:mm tt");

        // Validation method
        public bool IsValid()
        {
            return !string.IsNullOrWhiteSpace(AboutYou) && 
                   AboutYou.Length <= 1000 &&
                   (Experiences?.All(e => e.IsValid()) ?? true);
        }

        // Method to get formatted profile for AI context
        public string GetFormattedProfileForAI()
        {
            var profile = new System.Text.StringBuilder();
            
            profile.AppendLine("=== CANDIDATE PROFILE ===");
            profile.AppendLine();
            
            profile.AppendLine("ABOUT:");
            profile.AppendLine(AboutYou);
            profile.AppendLine();

            if (CodingLanguages.Any())
            {
                profile.AppendLine("TECHNICAL SKILLS:");
                profile.AppendLine(string.Join(", ", CodingLanguages));
                profile.AppendLine();
            }

            if (Experiences.Any())
            {
                profile.AppendLine("WORK EXPERIENCE:");
                foreach (var exp in Experiences.OrderByDescending(e => e.StartDate))
                {
                    profile.AppendLine($"• {exp.CompanyName} ({exp.StartDate:MMM yyyy} - {(exp.IsCurrentProject ? "Present" : exp.EndDate?.ToString("MMM yyyy") ?? "Unknown")})");
                    if (!string.IsNullOrWhiteSpace(exp.ProjectDescription))
                    {
                        profile.AppendLine($"  {exp.ProjectDescription}");
                    }
                    if (exp.TechnologiesUsed.Any())
                    {
                        profile.AppendLine($"  Technologies: {string.Join(", ", exp.TechnologiesUsed)}");
                    }
                    profile.AppendLine();
                }
            }

            if (Achievements.Any())
            {
                profile.AppendLine("ACHIEVEMENTS:");
                foreach (var achievement in Achievements)
                {
                    profile.AppendLine($"• {achievement}");
                }
                profile.AppendLine();
            }

            profile.AppendLine("=== END PROFILE ===");
            
            return profile.ToString();
        }
    }

    [BsonIgnoreExtraElements]
    public class InterviewWorkExperience
    {
        [BsonElement("company_name")]
        [Required(ErrorMessage = "Company name is required")]
        [StringLength(200, ErrorMessage = "Company name cannot exceed 200 characters")]
        [Display(Name = "Company Name")]
        public string CompanyName { get; set; } = string.Empty;

        [BsonElement("start_date")]
        [Required(ErrorMessage = "Start date is required")]
        [Display(Name = "Start Date")]
        public DateTime StartDate { get; set; }

        [BsonElement("end_date")]
        [Display(Name = "End Date")]
        public DateTime? EndDate { get; set; }

        [BsonElement("project_description")]
        [StringLength(2000, ErrorMessage = "Project description cannot exceed 2000 characters")]
        [Display(Name = "Project Description")]
        public string ProjectDescription { get; set; } = string.Empty;

        [BsonElement("technologies_used")]
        [Display(Name = "Technologies Used")]
        public List<string> TechnologiesUsed { get; set; } = new List<string>();

        [BsonElement("is_current_project")]
        [Display(Name = "Current Position")]
        public bool IsCurrentProject { get; set; }

        // Helper properties for display
        [BsonIgnore]
        public string DateRange
        {
            get
            {
                var start = StartDate.ToString("MMM yyyy");
                var end = IsCurrentProject ? "Present" : (EndDate?.ToString("MMM yyyy") ?? "Unknown");
                return $"{start} - {end}";
            }
        }

        [BsonIgnore]
        public string TechnologiesDisplay => string.Join(", ", TechnologiesUsed);

        [BsonIgnore]
        public int DurationInMonths
        {
            get
            {
                var endDate = IsCurrentProject ? DateTime.UtcNow : (EndDate ?? DateTime.UtcNow);
                return Math.Max(0, ((endDate.Year - StartDate.Year) * 12) + endDate.Month - StartDate.Month);
            }
        }

        [BsonIgnore]
        public string DurationDisplay
        {
            get
            {
                var months = DurationInMonths;
                if (months < 12)
                    return $"{months} month{(months != 1 ? "s" : "")}";
                
                var years = months / 12;
                var remainingMonths = months % 12;
                
                if (remainingMonths == 0)
                    return $"{years} year{(years != 1 ? "s" : "")}";
                
                return $"{years} year{(years != 1 ? "s" : "")} {remainingMonths} month{(remainingMonths != 1 ? "s" : "")}";
            }
        }

        // Validation method
        public bool IsValid()
        {
            if (string.IsNullOrWhiteSpace(CompanyName))
                return false;

            if (StartDate == default)
                return false;

            if (!IsCurrentProject && EndDate.HasValue && EndDate < StartDate)
                return false;

            if (!string.IsNullOrWhiteSpace(ProjectDescription) && ProjectDescription.Length > 2000)
                return false;

            return true;
        }
    }
}
