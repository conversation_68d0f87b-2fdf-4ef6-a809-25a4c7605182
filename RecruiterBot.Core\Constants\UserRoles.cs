namespace RecruiterBot.Core.Constants
{
    public static class UserRoles
    {
        // Database role names (uppercase)
        public const string Admin = "ADMIN";
        public const string CorpAdmin = "CORP_ADMIN";
        public const string User = "USER";

        public static readonly string[] AllRoles = { Admin, CorpAdmin, User };

        public static string GetDisplayName(string role)
        {
            return role switch
            {
                "ADMIN" => "Administrator",
                "CORP_ADMIN" => "Corporate Admin",
                "USER" => "Standard User",
                _ => role
            };
        }

        public static string GetDescription(string role)
        {
            return role switch
            {
                "ADMIN" => "Full system access, can create Corporate Admins",
                "CORP_ADMIN" => "Can create and manage Standard users, view team data",
                "USER" => "Standard user access, can view data from their Corp Admin",
                _ => "Unknown role"
            };
        }

        public static bool CanCreateRole(string currentUserRole, string targetRole)
        {
            return currentUserRole switch
            {
                "ADMIN" => targetRole == "CORP_ADMIN",
                "CORP_ADMIN" => targetRole == "USER",
                _ => false
            };
        }

        public static string[] GetCreatableRoles(string currentUserRole)
        {
            return currentUserRole switch
            {
                "ADMIN" => new[] { "CORP_ADMIN" },
                "CORP_ADMIN" => new[] { "USER" },
                _ => Array.Empty<string>()
            };
        }
    }
}
