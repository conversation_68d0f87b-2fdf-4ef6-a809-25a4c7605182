@page "{id}"
@model RecruiterBot.Web.Pages.Consultant.InterviewProfiles.EditModel
@using RecruiterBot.Core.Constants
@{
    ViewData["Title"] = "Edit Interview Profile";
}

@section Styles {
    <style>
        .section-card {
            border-left: 4px solid #0d6efd;
        }
        .tech-tag {
            background-color: #e9ecef;
            border: 1px solid #ced4da;
            border-radius: 15px;
            padding: 5px 12px;
            margin: 2px;
            display: inline-block;
            font-size: 0.875rem;
        }
        .tech-tag .remove-tech {
            margin-left: 8px;
            cursor: pointer;
            color: #dc3545;
        }
        .experience-item {
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 15px;
            background-color: #f8f9fa;
        }
        .achievement-item {
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 10px;
            margin-bottom: 10px;
            background-color: #f8f9fa;
        }
        .add-button {
            border: 2px dashed #0d6efd;
            background-color: transparent;
            color: #0d6efd;
        }
        .add-button:hover {
            background-color: rgba(13, 110, 253, 0.1);
        }
    </style>
}

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <!-- Header -->
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h2 class="text-primary mb-1">
                        <i class="bi bi-pencil me-2"></i>
                        Edit Interview Profile
                    </h2>
                    <p class="text-muted mb-0">Update your profile for AI-powered interview preparation</p>
                </div>
                <a asp-page="./Index" class="btn btn-secondary">
                    <i class="bi bi-arrow-left me-2"></i>
                    Back to Profiles
                </a>
            </div>

            <!-- Form -->
            <form method="post" id="profileForm">
                @Html.AntiForgeryToken()
                <input asp-for="Input.Id" type="hidden" />
                
                <!-- Error Summary -->
                <div asp-validation-summary="All" class="alert alert-danger" role="alert"></div>

                <!-- About You Section -->
                <div class="card section-card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="bi bi-person-circle me-2"></i>About You
                        </h5>
                        <small class="text-muted">This will be used for "Tell me about yourself" type questions</small>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <label asp-for="Input.AboutYou" class="form-label">Professional Summary *</label>
                            <textarea asp-for="Input.AboutYou" class="form-control" rows="5" 
                                      placeholder="Describe your professional background, key skills, and career highlights..."></textarea>
                            <div class="form-text">
                                <span id="aboutYouCount">0</span>/1000 characters
                            </div>
                            <span asp-validation-for="Input.AboutYou" class="text-danger"></span>
                        </div>
                    </div>
                </div>

                <!-- Coding Languages Section -->
                <div class="card section-card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="bi bi-code-slash me-2"></i>Technologies & Programming Languages
                        </h5>
                        <small class="text-muted">Add technologies, programming languages, frameworks, and tools you know</small>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <label class="form-label">Add Technology</label>
                            <div class="input-group">
                                <input type="text" id="techInput" class="form-control" 
                                       placeholder="e.g., JavaScript, React, Python, AWS...">
                                <button type="button" class="btn btn-outline-primary" onclick="addTechnology()">
                                    <i class="bi bi-plus"></i> Add
                                </button>
                            </div>
                        </div>
                        <div id="technologiesContainer">
                            <!-- Technologies will be added here dynamically -->
                        </div>
                        <input type="hidden" asp-for="Input.CodingLanguagesJson" id="codingLanguagesJson" />
                    </div>
                </div>

                <!-- Work Experience Section -->
                <div class="card section-card mb-4">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <div>
                            <h5 class="mb-0">
                                <i class="bi bi-briefcase me-2"></i>Work Experience
                            </h5>
                            <small class="text-muted">Add your professional work experience and projects</small>
                        </div>
                        <button type="button" class="btn btn-sm btn-outline-primary" onclick="addExperience()">
                            <i class="bi bi-plus"></i> Add Experience
                        </button>
                    </div>
                    <div class="card-body">
                        <div id="experiencesContainer">
                            <!-- Experiences will be added here dynamically -->
                        </div>
                        <button type="button" class="btn add-button w-100" onclick="addExperience()">
                            <i class="bi bi-plus-circle me-2"></i>Add Work Experience
                        </button>
                        <input type="hidden" asp-for="Input.ExperiencesJson" id="experiencesJson" />
                    </div>
                </div>

                <!-- Achievements Section -->
                <div class="card section-card mb-4">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <div>
                            <h5 class="mb-0">
                                <i class="bi bi-trophy me-2"></i>Professional Achievements
                            </h5>
                            <small class="text-muted">Add awards, certifications, notable accomplishments</small>
                        </div>
                        <button type="button" class="btn btn-sm btn-outline-primary" onclick="addAchievement()">
                            <i class="bi bi-plus"></i> Add Achievement
                        </button>
                    </div>
                    <div class="card-body">
                        <div id="achievementsContainer">
                            <!-- Achievements will be added here dynamically -->
                        </div>
                        <button type="button" class="btn add-button w-100" onclick="addAchievement()">
                            <i class="bi bi-plus-circle me-2"></i>Add Achievement
                        </button>
                        <input type="hidden" asp-for="Input.AchievementsJson" id="achievementsJson" />
                    </div>
                </div>

                <!-- Actions -->
                <div class="row">
                    <div class="col-12">
                        <hr>
                        <div class="d-flex justify-content-end gap-2">
                            <a asp-page="./Index" class="btn btn-secondary">
                                <i class="bi bi-x-circle me-2"></i>Cancel
                            </a>
                            <button type="submit" class="btn btn-primary" id="submitBtn">
                                <i class="bi bi-check-circle me-2"></i>
                                <span class="btn-text">Update Profile</span>
                                <span class="spinner-border spinner-border-sm d-none" role="status"></span>
                            </button>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>

@section Scripts {
    <partial name="_ValidationScriptsPartial" />
    
    <script>
        let experienceCounter = 0;
        let achievementCounter = 0;
        let technologies = [];
        let experiences = [];
        let achievements = [];

        // Initialize data from server
        document.addEventListener('DOMContentLoaded', function() {
            // Load existing data
            try {
                technologies = JSON.parse('@Html.Raw(Model.Input.CodingLanguagesJson)') || [];
                experiences = JSON.parse('@Html.Raw(Model.Input.ExperiencesJson)') || [];
                achievements = JSON.parse('@Html.Raw(Model.Input.AchievementsJson)') || [];
            } catch (e) {
                console.error('Error parsing existing data:', e);
                technologies = [];
                experiences = [];
                achievements = [];
            }

            // Populate UI with existing data
            updateTechnologiesDisplay();
            loadExistingExperiences();
            loadExistingAchievements();

            // Trigger character count update
            const aboutYouField = document.getElementById('Input_AboutYou');
            if (aboutYouField) {
                aboutYouField.dispatchEvent(new Event('input'));
            }
        });

        // Character counter for About You
        document.getElementById('Input_AboutYou').addEventListener('input', function() {
            const count = this.value.length;
            document.getElementById('aboutYouCount').textContent = count;
            
            if (count > 1000) {
                document.getElementById('aboutYouCount').style.color = '#dc3545';
            } else {
                document.getElementById('aboutYouCount').style.color = '#6c757d';
            }
        });

        // Technology management
        function addTechnology() {
            const input = document.getElementById('techInput');
            const tech = input.value.trim();
            
            if (tech && !technologies.includes(tech)) {
                technologies.push(tech);
                updateTechnologiesDisplay();
                updateTechnologiesJson();
                input.value = '';
            }
        }

        function removeTechnology(tech) {
            technologies = technologies.filter(t => t !== tech);
            updateTechnologiesDisplay();
            updateTechnologiesJson();
        }

        function updateTechnologiesDisplay() {
            const container = document.getElementById('technologiesContainer');
            container.innerHTML = '';
            
            technologies.forEach(tech => {
                const tag = document.createElement('span');
                tag.className = 'tech-tag';
                tag.innerHTML = `${tech} <span class="remove-tech" onclick="removeTechnology('${tech}')">&times;</span>`;
                container.appendChild(tag);
            });
        }

        function updateTechnologiesJson() {
            document.getElementById('codingLanguagesJson').value = JSON.stringify(technologies);
        }

        // Allow Enter key to add technology
        document.getElementById('techInput').addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                e.preventDefault();
                addTechnology();
            }
        });

        // Load existing experiences
        function loadExistingExperiences() {
            experiences.forEach(exp => {
                addExperienceWithData(exp);
            });
        }

        // Experience management
        function addExperience() {
            addExperienceWithData(null);
        }

        function addExperienceWithData(expData) {
            const container = document.getElementById('experiencesContainer');
            const index = experienceCounter++;
            
            const experienceHtml = `
                <div class="experience-item" data-index="${index}">
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <h6 class="mb-0">Experience #${index + 1}</h6>
                        <button type="button" class="btn btn-sm btn-outline-danger" onclick="removeExperience(${index})">
                            <i class="bi bi-trash"></i>
                        </button>
                    </div>
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label class="form-label">Company Name *</label>
                            <input type="text" class="form-control" data-field="companyName" value="${expData?.companyName || ''}" required>
                        </div>
                        <div class="col-md-3 mb-3">
                            <label class="form-label">Start Date *</label>
                            <input type="month" class="form-control" data-field="startDate" value="${expData?.startDate || ''}" required>
                        </div>
                        <div class="col-md-3 mb-3">
                            <label class="form-label">End Date</label>
                            <input type="month" class="form-control" data-field="endDate" value="${expData?.endDate || ''}" ${expData?.isCurrentProject ? 'disabled' : ''}>
                        </div>
                        <div class="col-12 mb-3">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" data-field="isCurrentProject" ${expData?.isCurrentProject ? 'checked' : ''} onchange="toggleEndDate(${index})">
                                <label class="form-check-label">This is my current position</label>
                            </div>
                        </div>
                        <div class="col-12 mb-3">
                            <label class="form-label">Project Description</label>
                            <textarea class="form-control" rows="3" data-field="projectDescription" 
                                      placeholder="Describe your role, responsibilities, and key projects...">${expData?.projectDescription || ''}</textarea>
                        </div>
                        <div class="col-12 mb-3">
                            <label class="form-label">Technologies Used</label>
                            <input type="text" class="form-control" data-field="technologiesUsed" 
                                   placeholder="e.g., React, Node.js, MongoDB (comma-separated)" value="${expData?.technologiesUsed?.join(', ') || ''}">
                        </div>
                    </div>
                </div>
            `;
            
            container.insertAdjacentHTML('beforeend', experienceHtml);
            updateExperiencesJson();
        }

        function removeExperience(index) {
            const element = document.querySelector(`[data-index="${index}"]`);
            if (element) {
                element.remove();
                updateExperiencesJson();
            }
        }

        function toggleEndDate(index) {
            const container = document.querySelector(`[data-index="${index}"]`);
            const checkbox = container.querySelector('[data-field="isCurrentProject"]');
            const endDateInput = container.querySelector('[data-field="endDate"]');
            
            if (checkbox.checked) {
                endDateInput.disabled = true;
                endDateInput.value = '';
            } else {
                endDateInput.disabled = false;
            }
            
            updateExperiencesJson();
        }

        function updateExperiencesJson() {
            const experienceElements = document.querySelectorAll('.experience-item');
            experiences = [];
            
            experienceElements.forEach(element => {
                const exp = {
                    companyName: element.querySelector('[data-field="companyName"]').value,
                    startDate: element.querySelector('[data-field="startDate"]').value,
                    endDate: element.querySelector('[data-field="endDate"]').value,
                    projectDescription: element.querySelector('[data-field="projectDescription"]').value,
                    technologiesUsed: element.querySelector('[data-field="technologiesUsed"]').value.split(',').map(t => t.trim()).filter(t => t),
                    isCurrentProject: element.querySelector('[data-field="isCurrentProject"]').checked
                };
                
                if (exp.companyName) {
                    experiences.push(exp);
                }
            });
            
            document.getElementById('experiencesJson').value = JSON.stringify(experiences);
        }

        // Load existing achievements
        function loadExistingAchievements() {
            achievements.forEach(achievement => {
                addAchievementWithData(achievement);
            });
        }

        // Achievement management
        function addAchievement() {
            addAchievementWithData('');
        }

        function addAchievementWithData(achievementText) {
            const container = document.getElementById('achievementsContainer');
            const index = achievementCounter++;
            
            const achievementHtml = `
                <div class="achievement-item" data-achievement-index="${index}">
                    <div class="d-flex gap-2">
                        <input type="text" class="form-control" placeholder="e.g., AWS Certified Solutions Architect, Employee of the Year..." 
                               value="${achievementText}" onchange="updateAchievementsJson()">
                        <button type="button" class="btn btn-sm btn-outline-danger" onclick="removeAchievement(${index})">
                            <i class="bi bi-trash"></i>
                        </button>
                    </div>
                </div>
            `;
            
            container.insertAdjacentHTML('beforeend', achievementHtml);
            updateAchievementsJson();
        }

        function removeAchievement(index) {
            const element = document.querySelector(`[data-achievement-index="${index}"]`);
            if (element) {
                element.remove();
                updateAchievementsJson();
            }
        }

        function updateAchievementsJson() {
            const achievementElements = document.querySelectorAll('.achievement-item input');
            achievements = [];
            
            achievementElements.forEach(input => {
                const value = input.value.trim();
                if (value) {
                    achievements.push(value);
                }
            });
            
            document.getElementById('achievementsJson').value = JSON.stringify(achievements);
        }

        // Form submission
        document.getElementById('profileForm').addEventListener('submit', function() {
            // Update all JSON fields before submission
            updateTechnologiesJson();
            updateExperiencesJson();
            updateAchievementsJson();
            
            // Show loading state
            const submitBtn = document.getElementById('submitBtn');
            const btnText = submitBtn.querySelector('.btn-text');
            const spinner = submitBtn.querySelector('.spinner-border');
            
            submitBtn.disabled = true;
            btnText.textContent = 'Updating...';
            spinner.classList.remove('d-none');
        });

        // Add event listeners to experience fields
        document.addEventListener('change', function(e) {
            if (e.target.closest('.experience-item')) {
                updateExperiencesJson();
            }
        });
    </script>
}
