using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using RecruiterBot.Core.Models;
using RecruiterBot.Web.Services;
using System.ComponentModel.DataAnnotations;

namespace RecruiterBot.Web.Pages.Account
{
    public class SetupAccountModel : PageModel
    {
        private readonly UserManager<User> _userManager;
        private readonly SignInManager<User> _signInManager;
        private readonly IEmailService _emailService;
        private readonly ILogger<SetupAccountModel> _logger;

        public SetupAccountModel(
            UserManager<User> userManager,
            SignInManager<User> signInManager,
            IEmailService emailService,
            ILogger<SetupAccountModel> logger)
        {
            _userManager = userManager;
            _signInManager = signInManager;
            _emailService = emailService;
            _logger = logger;
        }

        [BindProperty]
        public InputModel Input { get; set; } = new();

        public string UserId { get; set; } = string.Empty;
        public string Token { get; set; } = string.Empty;
        public bool IsValidToken { get; set; }
        public new User User { get; set; } = new();
        public bool IsRegistration { get; set; }
        public bool IsLoggedIn { get; set; }

        public class InputModel
        {
            [DataType(DataType.Password)]
            [Display(Name = "Temporary Password")]
            public string TemporaryPassword { get; set; } = string.Empty;

            [Required]
            [StringLength(100, ErrorMessage = "The {0} must be at least {2} and at max {1} characters long.", MinimumLength = 8)]
            [DataType(DataType.Password)]
            [Display(Name = "New Password")]
            public string NewPassword { get; set; } = string.Empty;

            [DataType(DataType.Password)]
            [Display(Name = "Confirm New Password")]
            [Compare("NewPassword", ErrorMessage = "The new password and confirmation password do not match.")]
            public string ConfirmPassword { get; set; } = string.Empty;
        }

        public async Task<IActionResult> OnGetAsync(string userId, string token, bool registration = false)
        {
            if (string.IsNullOrEmpty(userId) || string.IsNullOrEmpty(token))
            {
                TempData["ErrorMessage"] = "Invalid setup link.";
                return RedirectToPage("./Login");
            }

            var user = await _userManager.FindByIdAsync(userId);
            if (user == null)
            {
                TempData["ErrorMessage"] = "Invalid setup link.";
                return RedirectToPage("./Login");
            }

            // Check if token matches and hasn't expired
            if (user.ActivationToken != token ||
                user.ActivationTokenExpiry == null ||
                user.ActivationTokenExpiry < DateTime.UtcNow)
            {
                TempData["ErrorMessage"] = "This setup link has expired or is invalid. Please contact your administrator for a new setup link.";
                return RedirectToPage("./Login");
            }

            // Check if account is already activated and password changed
            if (user.IsActivated && !user.MustChangePassword && !user.IsTemporaryPassword)
            {
                TempData["InfoMessage"] = "Your account is already set up. You can log in with your credentials.";
                return RedirectToPage("./Login");
            }

            // Check if user is already logged in (came from login redirect)
            var currentUser = await _userManager.GetUserAsync(HttpContext.User);
            IsLoggedIn = currentUser != null && currentUser.Id == userId;

            UserId = userId;
            Token = token;
            User = user;
            IsValidToken = true;
            IsRegistration = registration;

            return Page();
        }

        public async Task<IActionResult> OnPostAsync(string userId, string token, bool registration = false)
        {
            if (string.IsNullOrEmpty(userId) || string.IsNullOrEmpty(token))
            {
                TempData["ErrorMessage"] = "Invalid setup request.";
                return RedirectToPage("./Login");
            }

            // Check if user is already logged in to determine if temporary password is required
            var loggedInUser = await _userManager.GetUserAsync(HttpContext.User);
            var isLoggedIn = loggedInUser != null && loggedInUser.Id == userId;

            // Add custom validation for temporary password if user is not logged in
            if (!isLoggedIn && string.IsNullOrEmpty(Input.TemporaryPassword))
            {
                ModelState.AddModelError(nameof(Input.TemporaryPassword), "The temporary password is required.");
            }

            if (!ModelState.IsValid)
            {
                var user = await _userManager.FindByIdAsync(userId);
                if (user != null)
                {
                    UserId = userId;
                    Token = token;
                    User = user;
                    IsValidToken = true;
                    IsRegistration = registration;
                    IsLoggedIn = isLoggedIn;
                }
                return Page();
            }

            var targetUser = await _userManager.FindByIdAsync(userId);
            if (targetUser == null)
            {
                TempData["ErrorMessage"] = "Invalid setup request.";
                return RedirectToPage("./Login");
            }

            // Verify token again
            if (targetUser.ActivationToken != token || 
                targetUser.ActivationTokenExpiry == null || 
                targetUser.ActivationTokenExpiry < DateTime.UtcNow)
            {
                TempData["ErrorMessage"] = "This setup link has expired or is invalid.";
                return RedirectToPage("./Login");
            }

            try
            {
                // Check if user is already logged in (came from login redirect)
                var currentlyLoggedInUser = await _userManager.GetUserAsync(HttpContext.User);
                var currentUserIsLoggedIn = currentlyLoggedInUser != null && currentlyLoggedInUser.Id == userId;

                string currentPassword = Input.TemporaryPassword;

                // If user is not logged in, verify the temporary password
                if (!currentUserIsLoggedIn)
                {
                    var passwordValid = await _userManager.CheckPasswordAsync(targetUser, Input.TemporaryPassword);
                    if (!passwordValid)
                    {
                        ModelState.AddModelError(nameof(Input.TemporaryPassword), "The temporary password is incorrect.");
                        UserId = userId;
                        Token = token;
                        User = targetUser;
                        IsValidToken = true;
                        IsRegistration = registration;
                        IsLoggedIn = isLoggedIn;
                        return Page();
                    }
                }
                else
                {
                    // User is logged in, so we need to get their current password hash to change it
                    // We'll use a different approach - remove the old password and add the new one
                    var removePasswordResult = await _userManager.RemovePasswordAsync(targetUser);
                    if (!removePasswordResult.Succeeded)
                    {
                        foreach (var error in removePasswordResult.Errors)
                        {
                            ModelState.AddModelError(string.Empty, error.Description);
                        }
                        UserId = userId;
                        Token = token;
                        User = targetUser;
                        IsValidToken = true;
                        IsRegistration = registration;
                        IsLoggedIn = isLoggedIn;
                        return Page();
                    }

                    var addPasswordResult = await _userManager.AddPasswordAsync(targetUser, Input.NewPassword);
                    if (!addPasswordResult.Succeeded)
                    {
                        foreach (var error in addPasswordResult.Errors)
                        {
                            ModelState.AddModelError(string.Empty, error.Description);
                        }
                        UserId = userId;
                        Token = token;
                        User = targetUser;
                        IsValidToken = true;
                        IsRegistration = registration;
                        IsLoggedIn = isLoggedIn;
                        return Page();
                    }

                    // Skip the change password step since we already handled it
                    goto UpdateUserProperties;
                }

                // Change the password (for non-logged-in users)
                var changePasswordResult = await _userManager.ChangePasswordAsync(targetUser, Input.TemporaryPassword, Input.NewPassword);
                if (!changePasswordResult.Succeeded)
                {
                    foreach (var error in changePasswordResult.Errors)
                    {
                        ModelState.AddModelError(string.Empty, error.Description);
                    }
                    UserId = userId;
                    Token = token;
                    User = targetUser;
                    IsValidToken = true;
                    IsRegistration = registration;
                    IsLoggedIn = false;
                    return Page();
                }

                UpdateUserProperties:
                // Update user activation and password tracking
                targetUser.IsActivated = true;
                targetUser.EmailConfirmed = true;
                targetUser.MustChangePassword = false;
                targetUser.IsTemporaryPassword = false;
                targetUser.PasswordChangedAt = DateTime.UtcNow;
                targetUser.ActivationToken = null;
                targetUser.ActivationTokenExpiry = null;

                var updateResult = await _userManager.UpdateAsync(targetUser);
                if (updateResult.Succeeded)
                {
                    _logger.LogInformation("Account setup completed successfully for user {UserId}", targetUser.Id);

                    // Send welcome email
                    try
                    {
                        var loginUrl = Url.Page("/Account/Login", pageHandler: null, values: null, protocol: Request.Scheme);
                        await _emailService.SendWelcomeEmailAsync(targetUser.Email, targetUser.FirstName, loginUrl);
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "Failed to send welcome email to {Email}", targetUser.Email);
                    }

                    TempData["SuccessMessage"] = "Your account has been set up successfully! You can now log in with your new password.";
                    return RedirectToPage("./Login");
                }
                else
                {
                    foreach (var error in updateResult.Errors)
                    {
                        ModelState.AddModelError(string.Empty, error.Description);
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error during account setup for user {UserId}", userId);
                ModelState.AddModelError(string.Empty, "An error occurred during account setup. Please try again.");
            }

            var authenticatedUser = await _userManager.GetUserAsync(HttpContext.User);
            var userIsLoggedIn = authenticatedUser != null && authenticatedUser.Id == userId;

            UserId = userId;
            Token = token;
            User = targetUser;
            IsValidToken = true;
            IsRegistration = registration;
            IsLoggedIn = userIsLoggedIn;
            return Page();
        }
    }
}
